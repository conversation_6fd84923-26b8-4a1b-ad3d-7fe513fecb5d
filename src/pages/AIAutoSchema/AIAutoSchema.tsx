
import { FormControlLabel, FormGroup, styled, Switch, SwitchProps } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import { useEffect, useRef, useState } from "react";
import { NavLink, useLoaderData, useNavigate, useRouteLoaderData, useSearchParams } from "react-router-dom";
import AbunButton from "../../components/AbunButton/AbunButton";
import AbunModal from "../../components/AbunModal/AbunModal";
import AbunTable from "../../components/AbunTable/AbunTable";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import Icon from "../../components/Icon/Icon";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { withAdminAndProductionCheck } from "../../utils/adminAndProductionCheck";

import {
    addSitemap,
    bulkUpdateWebpageSchemaSettingMutation,
    checkJobStatus,
    findWebsiteSitemaps,
    generateWebpagesSchema,
    getJobStatusQueryFn,
    getScrapedWebPages,
    getWebsiteAnalysisStatsQueryFn,
    markToolAsUsedMutation,
    saveSummaryAndSchemaMutation,
    scrapeMoreWebsitePages,
    toggleAutoScanWebsiteMutation,
    toggleAutoSchemaMutation,
    updateWebpageSchemaSettingMutation,
    verifyToolsLoadingScriptMutation,
} from "../../utils/api";
import { BasePageData } from "../Base/Base";
import { ConnectWebsite } from "../ConnectWebsite/LazyConnectWebsiteModal";
import { pageURL } from "../routes";
import './AIAutoSchema.min.css';

interface WebsiteScanData {
    url: string;
    title: string;
    schema: string;
    lastScanned: string;
    schemaFound: boolean;
    autoSchemaEnabled: boolean;
    schemaGenerated: boolean;
}

interface CustomizedSwitchProps {
    url: string;
    autoSchemaEnabled: boolean;
    handleToggleAutoSchema: (url: string, currentState: boolean) => void;
    disabled?: boolean;
}

interface GlobalSwitchProps {
    enabled: boolean;
    handleToggle: (enabled: boolean) => void;
    disabled?: boolean;
}

interface ServerData {
    website_connected: boolean
    sitemap_urls: Array<string>
    domain: string
    is_crawling: boolean
    finding_sitemaps: boolean
    has_more_pages: boolean
    total_pages_found?: number
    total_pages_without_schema: number
    pages_with_auto_schema_live: number
    auto_scraping_enabled: boolean
    auto_schema_enabled: boolean
    tools_loading_script_verified: boolean
    next_run_time: string
    encrypted_id: string
    auto_schema_used: boolean
    auto_schema_limit: number
    auto_schema_remaining_limit: number
}

interface WebsiteAanalysisStats {
    total_pages: number,
    pages_scanned: number,
    progress: number,
    estimated_time_left: number,
    steps: {
        crawling: string,
        analyzing: string,
        generating: string
    },
    is_analysis_complete: boolean,
    time_display: string
}

function CustomizedSwitch({ url, autoSchemaEnabled, handleToggleAutoSchema, disabled = false }: CustomizedSwitchProps) {
    const IOSSwitch = styled((props: SwitchProps) => (
        <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
    ))(({ theme }) => ({
        width: 42,
        height: 26,
        padding: 0,
        '& .MuiSwitch-switchBase': {
            padding: 0,
            margin: 2,
            transitionDuration: '300ms',
            '&.Mui-checked': {
                transform: 'translateX(16px)',
                color: '#fff',
                '& + .MuiSwitch-track': {
                    backgroundColor: '#65C466',
                    opacity: 1,
                    border: 0,
                    ...theme.applyStyles('dark', {
                        backgroundColor: '#2ECA45',
                    }),
                },
                '&.Mui-disabled + .MuiSwitch-track': {
                    opacity: 0.5,
                },
            },
            '&.Mui-focusVisible .MuiSwitch-thumb': {
                color: '#33cf4d',
                border: '6px solid #fff',
            },
            '&.Mui-disabled .MuiSwitch-thumb': {
                color: theme.palette.grey[100],
                ...theme.applyStyles('dark', {
                    color: theme.palette.grey[600],
                }),
            },
            '&.Mui-disabled + .MuiSwitch-track': {
                opacity: 0.7,
                ...theme.applyStyles('dark', {
                    opacity: 0.3,
                }),
            },
        },
        '& .MuiSwitch-thumb': {
            boxSizing: 'border-box',
            width: 22,
            height: 22,
        },
        '& .MuiSwitch-track': {
            borderRadius: 26 / 2,
            backgroundColor: '#E9E9EA',
            opacity: 1,
            transition: theme.transitions.create(['background-color'], {
                duration: 500,
            }),
            ...theme.applyStyles('dark', {
                backgroundColor: '#39393D',
            }),
        },
    }));

    return (
        <FormGroup sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
            <FormControlLabel
                control={
                    <IOSSwitch
                        sx={{ m: 1 }}
                        checked={autoSchemaEnabled}
                        disabled={disabled}
                        onChange={() => handleToggleAutoSchema(url, autoSchemaEnabled)}
                    />
                }
                label=""
            />
        </FormGroup>
    );
}

function GlobalSwitch({ enabled, handleToggle, disabled = false }: GlobalSwitchProps) {
    const IOSSwitch = styled((props: SwitchProps) => (
        <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
    ))(({ theme }) => ({
        width: 42,
        height: 26,
        padding: 0,
        '& .MuiSwitch-switchBase': {
            padding: 0,
            margin: 2,
            transitionDuration: '300ms',
            '&.Mui-checked': {
                transform: 'translateX(16px)',
                color: '#fff',
                '& + .MuiSwitch-track': {
                    backgroundColor: '#65C466',
                    opacity: 1,
                    border: 0,
                    ...theme.applyStyles('dark', {
                        backgroundColor: '#2ECA45',
                    }),
                },
                '&.Mui-disabled + .MuiSwitch-track': {
                    opacity: 0.5,
                },
            },
            '&.Mui-focusVisible .MuiSwitch-thumb': {
                color: '#33cf4d',
                border: '6px solid #fff',
            },
            '&.Mui-disabled .MuiSwitch-thumb': {
                color: theme.palette.mode === 'light' ? '#f3f3f3' : '#424242',
            },
            '&.Mui-disabled + .MuiSwitch-track': {
                opacity: theme.palette.mode === 'light' ? 0.7 : 0.3,
            },
        },
        '& .MuiSwitch-thumb': {
            boxSizing: 'border-box',
            width: 22,
            height: 22,
        },
        '& .MuiSwitch-track': {
            borderRadius: 26 / 2,
            backgroundColor: theme.palette.mode === 'light' ? '#E9E9EA' : '#39393D',
            opacity: 1,
            transition: theme.transitions.create(['background-color'], {
                duration: 500,
            }),
        },
    }));

    return (
        <FormGroup sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
            <FormControlLabel
                control={
                    <IOSSwitch
                        sx={{ m: 1 }}
                        checked={enabled}
                        disabled={disabled}
                        onChange={() => handleToggle(!enabled)}
                    />
                }
                label=""
            />
        </FormGroup>
    );
}

function AIAutoSchema() {
    // ---------------------- NON STATE CONSTANTS ----------------------
    const pageSizes = [15, 25, 50, 100];

    // ------------------------- QUERY PARAMETERS -----------------------
    const [searchParams] = useSearchParams();
    const tab = searchParams.get("tab");

    // ----------------------------- LOADER -----------------------------
    const pageData: ServerData = useLoaderData() as ServerData;
    const basePageData: BasePageData = useRouteLoaderData("base") as BasePageData;

    // ----------------------- REFS -----------------------
    const successAlertRef = useRef<any>(null);
    const failAlertRef = useRef<any>(null);

    // -------------------------- NAVIGATOR --------------------------
    const navigate = useNavigate();

    // -------------------------- STATES --------------------------
    const [activeTab, setActiveTab] = useState(tab || "dashboard");
    const [tableData, setTableData] = useState<Array<WebsiteScanData>>([]);
    const [isCrawling, setIsCrawling] = useState(pageData.is_crawling);
    const [sitemapUrl, setSitemapUrl] = useState(pageData.sitemap_urls[0] || "");
    const [showConnectWebsiteModal, setShowConnectWebsiteModal] = useState(!pageData.website_connected);
    const [showSitemapUrlModal, setShowSitemapUrlModal] = useState(pageData.sitemap_urls.length === 0 && !pageData.finding_sitemaps && basePageData.user_verified);
    const [isLoading, setIsLoading] = useState(pageData.finding_sitemaps);
    const [loadingDataText, setLoadingDataText] = useState(pageData.finding_sitemaps ? "Finding Sitemaps..." : "Loading Data...");
    const [websiteAnalysisStats, setWebsiteAnalysisStats] = useState<WebsiteAanalysisStats>();
    const [showSchemaModal, setShowSchemaModal] = useState(false);
    const [regenerateLink, setRegenerateLink] = useState("");
    const [editedSchema, setEditedSchema] = useState("");
    const [editedSchemaPageTitle, setEditedSchemaPageTitle] = useState("");
    const textareaRef = useRef<HTMLTextAreaElement | null>(null);
    const tableRef = useRef<{ refetchData: () => Promise<void> }>(null);
    const [jobId, setJobId] = useState("");
    const [analysisStepText, setAnalysisStepText] = useState("");
    const [bulkGenerateJobId, setBulkGenerateJobId] = useState("");
    const [isBulkGenerating, setIsBulkGenerating] = useState(false);
    const [generateJobIds, setGenerateJobIds] = useState<{ [key: string]: string }>({});
    const [disabledButtons, setDisabledButtons] = useState<{ [key: string]: boolean }>({});
    const [showAddSitemapsModal, setShowAddSitemapsModal] = useState(false);
    const [newSitemaps, setNewSitemaps] = useState("");
    const [isScanMore, setIsScanMore] = useState(false);
    const [scanMoreRunning, setScanMoreRunning] = useState(localStorage.getItem(`${pageData.domain}scanMoreRunning`) === "true");
    const [autoScrapingEnabled, setAutoScrapingEnabled] = useState(pageData.auto_scraping_enabled || false);
    const [autoSchemaEnabled, setAutoSchemaEnabled] = useState(pageData.auto_schema_enabled || false);
    const [isVerifying, setIsVerifying] = useState(false);
    const [isVerified, setIsVerified] = useState(pageData.tools_loading_script_verified);
    const [autoSchemaUsed, setAutoSchemaUsed] = useState(pageData.auto_schema_used);
    const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
    const [selectAll, setSelectAll] = useState(false);
    const [autoSchemaRemainingLimit, setAutoSchemaRemainingLimit] = useState(pageData.auto_schema_remaining_limit);
    const [showBulkActionModal, setShowBulkActionModal] = useState(false);
    const [bulkAction, setBulkAction] = useState<'enable' | 'disable'>('enable');
    const [pageToggleStates, setPageToggleStates] = useState<Map<string, boolean>>(new Map());
    const [liveSchemaCount, setLiveSchemaCount] = useState(pageData.pages_with_auto_schema_live || 0);

    // -------------------------- QUERIES --------------------------
    const { isFetching, isError, data, refetch } = useQuery(getScrapedWebPages());
    const {
        data: websiteAnalysisStatsData,
    } = useQuery({
        queryKey: ['websiteAnalysisStats'],
        queryFn: () => getWebsiteAnalysisStatsQueryFn(),
        refetchInterval: 5000,
        refetchIntervalInBackground: false,
        enabled: isCrawling,
    });

    const regenerateQuery = checkJobStatus(jobId);

    useQuery({
        ...regenerateQuery,
        refetchInterval: 5000,
        enabled: !!jobId,
        onSuccess: (result: any) => {
            if (result.data.status === "completed") {
                setDisabledButtons(prev => ({ ...prev, [regenerateLink]: false }));
                refetch();
                clearJobFromStorage(regenerateLink, 'regenerate');
                setJobId("");
            } else if (result.data.status === "failed") {
                setDisabledButtons(prev => ({ ...prev, [regenerateLink]: false }));
                clearJobFromStorage(regenerateLink, 'regenerate');
                setJobId("");
                failAlertRef.current?.show(`Schema regeneration failed for ${regenerateLink}`);
                setTimeout(() => {
                    failAlertRef.current?.close();
                }, 5000);
            }
        },
        onError: () => {
            setDisabledButtons(prev => ({ ...prev, [regenerateLink]: false }));
            clearJobFromStorage(regenerateLink, 'regenerate');
            setJobId("");
        },
    });

    // Track individual schema generation jobs
    useEffect(() => {
        const activeJobs = Object.entries(generateJobIds).filter(([_, jobId]) => jobId && jobId !== "generating");

        if (activeJobs.length === 0) return;

        // Group jobs by jobId to avoid duplicate requests for the same job
        const jobGroups = activeJobs.reduce((groups, [pageUrl, jobId]) => {
            if (!groups[jobId]) {
                groups[jobId] = [];
            }
            groups[jobId].push(pageUrl);
            return groups;
        }, {} as { [jobId: string]: string[] });

        // Skip individual job tracking if this job is being tracked as a bulk job
        const filteredJobGroups = Object.entries(jobGroups).filter(([jobId, _]) => jobId !== bulkGenerateJobId);

        if (filteredJobGroups.length === 0) return;

        const interval = setInterval(async () => {
            for (const [jobId, pageUrls] of filteredJobGroups) {
                try {
                    const response = await getJobStatusQueryFn(jobId);
                    const result = response.data;

                    if (result.status === "completed") {
                        setGenerateJobIds(prev => {
                            const newState = { ...prev };
                            pageUrls.forEach(pageUrl => {
                                delete newState[pageUrl];
                            });
                            return newState;
                        });
                        pageUrls.forEach(pageUrl => {
                            clearJobFromStorage(pageUrl, 'single');
                        });
                        refetch(); // Refresh table data
                    } else if (result.status === "failed") {
                        setGenerateJobIds(prev => {
                            const newState = { ...prev };
                            pageUrls.forEach(pageUrl => {
                                delete newState[pageUrl];
                            });
                            return newState;
                        });
                        pageUrls.forEach(pageUrl => {
                            clearJobFromStorage(pageUrl, 'single');
                        });
                        failAlertRef.current?.show(`Schema generation failed for ${pageUrls.length > 1 ? 'multiple pages' : pageUrls[0]}`);
                        setTimeout(() => {
                            failAlertRef.current?.close();
                        }, 5000);
                    }
                } catch (error) {
                    console.error('Error checking job status:', error);
                }
            }
        }, 5000);

        return () => clearInterval(interval);
    }, [generateJobIds, refetch, bulkGenerateJobId]);

    // Track bulk generation job
    const bulkGenerateQuery = checkJobStatus(bulkGenerateJobId);
    useQuery({
        ...bulkGenerateQuery,
        refetchInterval: 5000,
        enabled: !!bulkGenerateJobId,
        onSuccess: (result: any) => {
            if (result.data.status === "completed") {
                setIsBulkGenerating(false);
                setBulkGenerateJobId("");

                // Clear individual job states for bulk generation
                const bulkJob = getBulkJobFromStorage();
                if (bulkJob && bulkJob.pagesUrls && Array.isArray(bulkJob.pagesUrls)) {
                    const newGenerateJobIds = { ...generateJobIds };
                    bulkJob.pagesUrls.forEach((pageUrl: string) => {
                        delete newGenerateJobIds[pageUrl];
                    });
                    setGenerateJobIds(newGenerateJobIds);
                }

                clearBulkJobFromStorage();
                refetch();
                successAlertRef.current?.show("Bulk schema generation completed successfully!");
                setTimeout(() => {
                    successAlertRef.current?.close();
                }, 5000);
            } else if (result.data.status === "failed") {
                setIsBulkGenerating(false);
                setBulkGenerateJobId("");

                // Clear individual job states for bulk generation
                const bulkJob = getBulkJobFromStorage();
                if (bulkJob && bulkJob.pagesUrls && Array.isArray(bulkJob.pagesUrls)) {
                    const newGenerateJobIds = { ...generateJobIds };
                    bulkJob.pagesUrls.forEach((pageUrl: string) => {
                        delete newGenerateJobIds[pageUrl];
                    });
                    setGenerateJobIds(newGenerateJobIds);
                }

                clearBulkJobFromStorage();
                failAlertRef.current?.show("Bulk schema generation failed. Please try again.");
                setTimeout(() => {
                    failAlertRef.current?.close();
                }, 5000);
            }
        },
        onError: () => {
            setIsBulkGenerating(false);
            setBulkGenerateJobId("");

            // Clear individual job states for bulk generation
            const bulkJob = getBulkJobFromStorage();
            if (bulkJob && bulkJob.pagesUrls && Array.isArray(bulkJob.pagesUrls)) {
                const newGenerateJobIds = { ...generateJobIds };
                bulkJob.pagesUrls.forEach((pageUrl: string) => {
                    delete newGenerateJobIds[pageUrl];
                });
                setGenerateJobIds(newGenerateJobIds);
            }

            clearBulkJobFromStorage();
            failAlertRef.current?.show("Bulk schema generation failed. Please try again.");
            setTimeout(() => {
                failAlertRef.current?.close();
            }, 5000);
        },
    });

    // -------------------------- MUTATIONS --------------------------
    const addSiteMapMutation = useMutation(addSitemap);
    const saveSchema = useMutation(saveSummaryAndSchemaMutation);
    const updateSchemaSettingMutation = useMutation(updateWebpageSchemaSettingMutation);
    const bulkUpdateSchemaSettingMutation = useMutation(bulkUpdateWebpageSchemaSettingMutation);
    const toggleAutoScanMutation = useMutation(toggleAutoScanWebsiteMutation);
    const toggleAutoSchemaMut = useMutation(toggleAutoSchemaMutation);
    const scanMorePagesMutation = useMutation(scrapeMoreWebsitePages);
    const verifyScriptMutation = useMutation(verifyToolsLoadingScriptMutation);
    const generateSchemaMutation = useMutation(generateWebpagesSchema);
    const markToolAsUsedMut = useMutation(markToolAsUsedMutation);

    // ----------------------- EFFECTS -----------------------
    useEffect(() => {
        document.title = "AI Auto Schema | Abun";
    }, []);

    // Restore job states from localStorage on component mount
    useEffect(() => {
        // Restore bulk generation job
        const bulkJob = getBulkJobFromStorage();
        if (bulkJob && bulkJob.jobId && !isJobTooOld(bulkJob.timestamp)) {
            setBulkGenerateJobId(bulkJob.jobId);
            setIsBulkGenerating(true);

            // Set generating state for all pages in the bulk job
            if (bulkJob.pagesUrls && Array.isArray(bulkJob.pagesUrls)) {
                const newGenerateJobIds: { [key: string]: string } = {};
                bulkJob.pagesUrls.forEach((pageUrl: string) => {
                    newGenerateJobIds[pageUrl] = bulkJob.jobId;
                });
                setGenerateJobIds(prev => ({ ...prev, ...newGenerateJobIds }));
            }
        } else if (bulkJob && isJobTooOld(bulkJob.timestamp)) {
            clearBulkJobFromStorage();
        }

        // Restore individual job states from localStorage
        const allStorageKeys = Object.keys(localStorage);
        const schemaJobKeys = allStorageKeys.filter(key =>
            key.startsWith('schema_job_single_') || key.startsWith('schema_job_regenerate_')
        );

        const newGenerateJobIds: { [key: string]: string } = {};
        const newDisabledButtons: { [key: string]: boolean } = {};

        schemaJobKeys.forEach(key => {
            const jobData = JSON.parse(localStorage.getItem(key) || '{}');
            if (jobData.jobId && jobData.pageUrl && !isJobTooOld(jobData.timestamp)) {
                if (jobData.type === 'single') {
                    newGenerateJobIds[jobData.pageUrl] = jobData.jobId;
                } else if (jobData.type === 'regenerate') {
                    setJobId(jobData.jobId);
                    setRegenerateLink(jobData.pageUrl);
                    newDisabledButtons[jobData.pageUrl] = true;
                }
            } else if (jobData.timestamp && isJobTooOld(jobData.timestamp)) {
                // Clean up old jobs
                localStorage.removeItem(key);
            }
        });

        if (Object.keys(newGenerateJobIds).length > 0) {
            setGenerateJobIds(prev => ({ ...prev, ...newGenerateJobIds }));
        }
        if (Object.keys(newDisabledButtons).length > 0) {
            setDisabledButtons(prev => ({ ...prev, ...newDisabledButtons }));
        }
    }, [pageData.domain]);

    useEffect(() => {
        if (showSitemapUrlModal) {
            setIsLoading(true);
            setLoadingDataText("Finding Sitemaps...");

            // Try to find the sitemap before showing the modal
            findWebsiteSitemaps().then((response) => {
                const responseData: any = response.data;
                if (responseData.success) {
                    setShowSitemapUrlModal(false);
                    setIsCrawling(true);
                }
            }).catch(error => {
                console.log(error);
            }).finally(() => {
                setIsLoading(false);
            })
        }
    }, []);

    useEffect(() => {
        if (showSchemaModal) {
            setTimeout(adjustTextareaHeight, 0);
        }
    }, [showSchemaModal, editedSchema]);


    useEffect(() => {
        if (data) {
            const newTableData = (data as any)['data']['web_pages'].map((page: any) => ({
                ...page,
                schemaFound: page.schemaFound || false,
                autoSchemaEnabled: page.autoSchemaEnabled || false,
                schemaGenerated: page.schemaGenerated || false
            }));
            setTableData(newTableData);

            // Initialize toggle states from the fresh data
            const newToggleStates = new Map<string, boolean>();
            newTableData.forEach((page: any) => {
                newToggleStates.set(page.url, page.autoSchemaEnabled || false);
            });
            setPageToggleStates(newToggleStates);

            // Clear selections when data changes and update selectAll state
            const currentSelectedRows = new Set(selectedRows);
            const validSelections = new Set<string>();

            newTableData.forEach((page: any) => {
                if (currentSelectedRows.has(page.url)) {
                    validSelections.add(page.url);
                }
            });

            setSelectedRows(validSelections);

            // Update selectAll state based on valid selections
            const allSelectableSelected = newTableData.length > 0 && newTableData.every((page: any) => validSelections.has(page.url));
            setSelectAll(allSelectableSelected);
        }
    }, [data]);

    useEffect(() => {
        if (websiteAnalysisStatsData && (websiteAnalysisStatsData as any)['data']) {
            setWebsiteAnalysisStats((websiteAnalysisStatsData as any)['data']['stats']);
        }
    }, [websiteAnalysisStatsData]);

    useEffect(() => {
        if (websiteAnalysisStats && websiteAnalysisStats.is_analysis_complete) {
            // Refresh the page when analysis is complete
            updateScanMoreRunning(false);
            window.location.reload();
        }
    }, [websiteAnalysisStats]);


    useEffect(() => {
        const steps = websiteAnalysisStats?.steps;

        if (!steps) return;
        if (steps.crawling === 'in_progress') {
            setAnalysisStepText("Crawling website pages");
        } else if (steps.analyzing === 'in_progress') {
            setAnalysisStepText("Analyzing page content");
        } else {
            setAnalysisStepText("Generating schema markup");
        }
    }, [websiteAnalysisStats?.steps]);

    // Update live schema count when toggle states or table data changes
    useEffect(() => {
        const newCount = calculateLiveSchemaCount();
        setLiveSchemaCount(newCount);
    }, [pageToggleStates, tableData, autoSchemaEnabled, pageData.tools_loading_script_verified]);

    // ---------------------- HELPER FUNCTIONS ----------------------
    const calculateLiveSchemaCount = () => {
        if (!tableData || tableData.length === 0) return 0;

        return tableData.filter(page => {
            const currentToggleState = pageToggleStates.get(page.url) ?? page.autoSchemaEnabled;
            return pageData.tools_loading_script_verified &&
                autoSchemaEnabled &&
                page.schemaGenerated &&
                currentToggleState;
        }).length;
    };

    // ---------------------- TABLE COLUMN DEFS ----------------------
    const columnHelper = createColumnHelper<WebsiteScanData>();
    const columnDefs: ColumnDef<any, any>[] = [
        columnHelper.display({
            id: 'select',
            header: () => (
                <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={(e) => {
                        const checked = e.target.checked;
                        setSelectAll(checked);
                        if (checked) {
                            // Select all rows (both schema generated and non-generated)
                            const selectableRows = tableData.map(row => row.url);
                            setSelectedRows(new Set(selectableRows));
                        } else {
                            setSelectedRows(new Set());
                        }
                    }}
                />
            ),
            cell: ({ row }) => {
                const isSelected = selectedRows.has(row.original.url);

                return (
                    <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={(e) => {
                            const newSelectedRows = new Set(selectedRows);
                            if (e.target.checked) {
                                newSelectedRows.add(row.original.url);
                            } else {
                                newSelectedRows.delete(row.original.url);
                            }
                            setSelectedRows(newSelectedRows);

                            // Update select all state
                            const allSelectableSelected = tableData.every(r => newSelectedRows.has(r.url));
                            setSelectAll(allSelectableSelected && tableData.length > 0);
                        }}
                    />
                );
            },
            meta: { align: 'center' }
        }),
        columnHelper.accessor((row: WebsiteScanData) => row.title, {
            id: 'title',
            header: "Title",
            cell: props => (
                <div style={{ color: '#000' }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                        {props.row.original.title}
                    </div>
                    <NavLink
                        to={props.row.original.url}
                        target="_blank"
                        style={{
                            fontSize: '0.85em',
                            color: '#666',
                            textDecoration: 'none',
                            display: 'block',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                        }}
                    >
                        {props.row.original.url}
                    </NavLink>
                </div>
            ),
            enableGlobalFilter: true,
            meta: { align: 'left' }
        }),
        columnHelper.accessor((row: WebsiteScanData) => row.lastScanned, {
            id: 'lastScanned',
            header: "Last Scanned",
            cell: props => {
                const selectedDate = props.row.original.lastScanned;

                if (!selectedDate) return <span style={{ color: '#000', cursor: "default" }}>---</span>;

                const getRelativeTime = (dateString: string) => {
                    const createdDateObj = new Date(dateString);
                    const now = new Date();
                    const timeDiff = now.getTime() - createdDateObj.getTime();

                    const seconds = Math.floor(Math.abs(timeDiff) / 1000);
                    const minutes = Math.floor(seconds / 60);
                    const hours = Math.floor(minutes / 60);
                    const days = Math.floor(hours / 24);

                    if (timeDiff < 0) {
                        // FUTURE TIME
                        if (seconds < 60) return "in a few seconds";
                        if (minutes < 60) return `in ${minutes} minute${minutes === 1 ? '' : 's'}`;
                        if (hours < 24) return `in ${hours} hour${hours === 1 ? '' : 's'}`;
                        return `in ${days} day${days === 1 ? '' : 's'}`;
                    }

                    // PAST TIME
                    if (seconds < 60) return "just now";
                    if (minutes < 60) return minutes === 1 ? "a minute ago" : `${minutes} minutes ago`;
                    if (hours < 24) return hours === 1 ? "an hour ago" : `${hours} hours ago`;
                    if (days > 30) {
                        const day = createdDateObj.getDate();
                        const month = createdDateObj.toLocaleString('default', { month: 'short' });
                        const year = createdDateObj.getFullYear().toString().slice(-2);
                        return `${day} ${month}, ${year}`;
                    }
                    return days === 1 ? "a day ago" : `${days} days ago`;
                };

                return (
                    <span style={{ color: '#000', cursor: "default" }}>
                        {getRelativeTime(selectedDate)}
                    </span>
                );
            },
            enableGlobalFilter: true,
            meta: { align: 'center' }
        }),
        columnHelper.accessor((row: WebsiteScanData) => row.schemaFound, {
            id: 'schemaFound',
            header: "Existing Schema Found",
            cell: props => (
                <span className={`tag is-rounded ${props.row.original.schemaFound ? 'is-success' : 'is-danger'}`}>
                    {props.row.original.schemaFound ? 'Yes' : 'No'}
                </span>
            ),
            enableGlobalFilter: false,
            meta: { align: 'center' }
        }),
        columnHelper.accessor((row: WebsiteScanData) => row.autoSchemaEnabled, {
            id: 'autoSchema',
            header: "Auto Schema",
            cell: props => {
                // Get the current toggle state from our separate state, fallback to original data
                const currentToggleState = pageToggleStates.get(props.row.original.url) ?? props.row.original.autoSchemaEnabled;

                // Determine if schema should be live based on all conditions
                const isSchemaLive = pageData.tools_loading_script_verified && autoSchemaEnabled && props.row.original.schemaGenerated && currentToggleState;

                return (
                    <span
                        className={`tag is-rounded ${isSchemaLive ? 'is-success' : 'is-light'}`}>
                        {isSchemaLive ? 'Live' : 'Disabled'}
                    </span>
                );
            },
            enableGlobalFilter: false,
            meta: { align: 'center' }
        }),
        columnHelper.display({
            id: 'enableDisable',
            header: "Enable/Disable",
            cell: ({ row }) => {
                // Get the current toggle state from our separate state, fallback to original data
                const currentToggleState = pageToggleStates.get(row.original.url) ?? row.original.autoSchemaEnabled;

                // Calculate if the switch should be enabled based on conditions
                const isEnabled = pageData.tools_loading_script_verified && autoSchemaEnabled && row.original.schemaGenerated;
                const switchState = isEnabled ? currentToggleState : false;

                return (
                    <CustomizedSwitch
                        url={row.original.url}
                        autoSchemaEnabled={switchState}
                        handleToggleAutoSchema={handleToggleAutoSchema}
                        disabled={!autoSchemaEnabled || !row.original.schemaGenerated}
                    />
                );
            },
            meta: { align: 'center' }
        }),
        columnHelper.display({
            id: 'viewSchema',
            header: "Schema Action",
            cell: (props) => {
                const schemaGenerated = (props.row.original as any).schemaGenerated;
                const schemaValue = (props.row.original as any).schema;
                const pageUrl = props.row.original.url;
                const isGenerating = generateJobIds[pageUrl];

                if (schemaGenerated) {
                    return (
                        <button
                            className="button is-primary is-outlined is-small more-rounded-borders"
                            onClick={() => handleEditSchema(schemaValue, props.row.original.url, props.row.original.title)}
                        >
                            View Schema
                        </button>
                    );
                } else {
                    return (
                        <button
                            className="button is-success is-outlined is-small more-rounded-borders"
                            onClick={() => handleGenerateSchema(pageUrl)}
                            disabled={!!isGenerating || autoSchemaRemainingLimit === 0}
                        >
                            {isGenerating ? "Generating..." : "Generate Schema"}
                        </button>
                    );
                }
            },
            meta: { align: 'center' }
        }),
    ];

    // ---------------------- HANDLERS ----------------------
    const handleEditSchema = (schema: string, url: string, pageTitle: string) => {
        setEditedSchema(schema);
        setEditedSchemaPageTitle(pageTitle);
        setRegenerateLink(url)
        setShowSchemaModal(true)
    }

    const adjustTextareaHeight = () => {
        if (textareaRef.current) {
            textareaRef.current.style.height = "auto"; // Reset height
            textareaRef.current.style.height = textareaRef.current.scrollHeight + "px"; // Adjust height
        }
    };



    const handleBackClick = () => {
        setIsCrawling(false)
        setIsScanMore(false)
        refetch()
    };

    const updateScanMoreRunning = (value: boolean) => {
        localStorage.setItem(`${pageData.domain}scanMoreRunning`, value.toString());
        setScanMoreRunning(value);
    };

    const handleRegenerate = (pageUrl: string) => {
        setRegenerateLink(pageUrl)
        setDisabledButtons(prev => ({ ...prev, [pageUrl]: true }));

        generateSchemaMutation.mutate(
            { pageData: [pageUrl] },
            {
                onSuccess: (data) => {
                    const response = data['data'];
                    if (!response.success) {
                        setDisabledButtons(prev => ({ ...prev, [pageUrl]: false }));
                        failAlertRef.current?.show(response.message || "Failed to regenerate schema. Please try again after some time.");
                        setTimeout(() => {
                            failAlertRef.current?.close();
                        }, 5000);
                        return;
                    }

                    const jobId = response.job_id;

                    // Update the auto schema limit
                    setAutoSchemaRemainingLimit(response.auto_schema_remaining_limit);

                    if (!jobId) {
                        setDisabledButtons(prev => ({ ...prev, [pageUrl]: false }));
                        return;
                    }

                    setJobId(jobId);
                    saveJobToStorage(pageUrl, jobId, 'regenerate');

                    successAlertRef.current?.show("Your schema regeneration request is being processed. Please wait.");

                    setTimeout(() => {
                        successAlertRef.current?.close();
                    }, 5000);
                },
                onError: () => {
                    failAlertRef.current?.show("Failed to regenerate schema. Please try again after some time.");
                    setDisabledButtons(prev => ({ ...prev, [pageUrl]: false }));
                    setTimeout(() => {
                        failAlertRef.current?.close();
                    }, 5000);
                },
            }
        );
    };

    const handleGenerateSchema = (pageUrl: string) => {
        setGenerateJobIds(prev => ({ ...prev, [pageUrl]: "generating" }));

        generateSchemaMutation.mutate(
            { pageData: [pageUrl] },
            {
                onSuccess: (data) => {
                    const response = data['data'];
                    if (!response.success) {
                        setGenerateJobIds(prev => {
                            const newState = { ...prev };
                            delete newState[pageUrl];
                            return newState;
                        });
                        failAlertRef.current?.show(response.message || "Failed to generate schema. Please try again after some time.");
                        setTimeout(() => {
                            failAlertRef.current?.close();
                        }, 5000);
                        return;
                    }

                    const jobId = response.job_id;
                    const pagesUrls = response.pages_urls || [pageUrl]; // Use pages_urls from API or fallback to single pageUrl

                    // Update the auto schema limit
                    setAutoSchemaRemainingLimit(response.auto_schema_remaining_limit);

                    if (jobId) {
                        // Update job IDs for all pages in the response
                        const newJobIds: { [key: string]: string } = {};
                        pagesUrls.forEach((url: string) => {
                            newJobIds[url] = jobId;
                            saveJobToStorage(url, jobId, 'single');
                        });

                        setGenerateJobIds(prev => ({ ...prev, ...newJobIds }));
                        successAlertRef.current?.show("Schema generation request is being processed. Please wait.");
                        setTimeout(() => {
                            successAlertRef.current?.close();
                        }, 5000);
                    } else {
                        setGenerateJobIds(prev => {
                            const newState = { ...prev };
                            delete newState[pageUrl];
                            return newState;
                        });
                    }
                },
                onError: () => {
                    failAlertRef.current?.show("Failed to generate schema. Please try again after some time.");
                    setGenerateJobIds(prev => {
                        const newState = { ...prev };
                        delete newState[pageUrl];
                        return newState;
                    });
                    setTimeout(() => {
                        failAlertRef.current?.close();
                    }, 5000);
                },
            }
        );
    };

    const handleBulkGenerateSchema = () => {
        setIsBulkGenerating(true);

        generateSchemaMutation.mutate(
            { pageData: [] }, // Pass an empty array to indicate bulk generation
            {
                onSuccess: (data) => {
                    const response = data['data'];
                    if (!response.success) {
                        setIsBulkGenerating(false);
                        failAlertRef.current?.show(response.message || "Failed to start bulk schema generation. Please try again after some time.");
                        setTimeout(() => {
                            failAlertRef.current?.close();
                        }, 5000);
                        return;
                    }

                    const jobId = response.job_id;
                    const pagesUrls = response.pages_urls || []; // Get pages_urls from API response

                    // Update the auto schema limit
                    setAutoSchemaRemainingLimit(response.auto_schema_remaining_limit);

                    if (jobId) {
                        setBulkGenerateJobId(jobId);
                        saveBulkJobToStorage(jobId, pagesUrls);

                        // Set generating state for all pages in the bulk job
                        if (pagesUrls && Array.isArray(pagesUrls)) {
                            const newGenerateJobIds: { [key: string]: string } = {};
                            pagesUrls.forEach((pageUrl: string) => {
                                newGenerateJobIds[pageUrl] = jobId;
                            });
                            setGenerateJobIds(prev => ({ ...prev, ...newGenerateJobIds }));
                        }

                        successAlertRef.current?.show("Bulk schema generation request is being processed. Please wait.");
                        setTimeout(() => {
                            successAlertRef.current?.close();
                        }, 5000);
                    } else {
                        setIsBulkGenerating(false);
                    }
                },
                onError: () => {
                    failAlertRef.current?.show("Failed to start bulk schema generation. Please try again after some time.");
                    setIsBulkGenerating(false);
                    setTimeout(() => {
                        failAlertRef.current?.close();
                    }, 5000);
                },
            }
        );
    };

    const handleGenerateSelectedSchema = () => {
        if (selectedRows.size === 0) {
            failAlertRef.current?.show("Please select at least one page to generate schema.");
            setTimeout(() => {
                failAlertRef.current?.close();
            }, 3000);
            return;
        }

        const selectedUrls = Array.from(selectedRows);

        // Set generating state for all selected pages
        const newGenerateJobIds: { [key: string]: string } = {};
        selectedUrls.forEach((pageUrl: string) => {
            newGenerateJobIds[pageUrl] = "generating";
        });
        setGenerateJobIds(prev => ({ ...prev, ...newGenerateJobIds }));

        // Clear selected rows immediately
        setSelectedRows(new Set());
        setSelectAll(false);

        // Use bulk-generate with selected URLs instead of individual single-generate calls
        generateSchemaMutation.mutate(
            { pageData: selectedUrls },
            {
                onSuccess: (data) => {
                    const response = data['data'];
                    if (!response.success) {
                        // Reset generating state for all selected pages on failure
                        setGenerateJobIds(prev => {
                            const newState = { ...prev };
                            selectedUrls.forEach(pageUrl => {
                                delete newState[pageUrl];
                            });
                            return newState;
                        });
                        failAlertRef.current?.show(response.message || "Failed to start schema generation for selected pages. Please try again after some time.");
                        setTimeout(() => {
                            failAlertRef.current?.close();
                        }, 5000);
                        return;
                    }

                    const jobId = response.job_id;
                    const pagesUrls = response.pages_urls || selectedUrls;

                    // Update the auto schema limit
                    setAutoSchemaRemainingLimit(response.auto_schema_remaining_limit);

                    if (jobId) {
                        // Update job IDs for all pages in the response
                        const newJobIds: { [key: string]: string } = {};
                        pagesUrls.forEach((url: string) => {
                            newJobIds[url] = jobId;
                            saveJobToStorage(url, jobId, 'single');
                        });

                        setGenerateJobIds(prev => ({ ...prev, ...newJobIds }));
                        successAlertRef.current?.show(`Schema generation started for ${selectedUrls.length} selected page${selectedUrls.length > 1 ? 's' : ''}.`);
                        setTimeout(() => {
                            successAlertRef.current?.close();
                        }, 5000);
                    } else {
                        // Reset generating state for all selected pages if no job ID
                        setGenerateJobIds(prev => {
                            const newState = { ...prev };
                            selectedUrls.forEach(pageUrl => {
                                delete newState[pageUrl];
                            });
                            return newState;
                        });
                    }
                },
                onError: () => {
                    // Reset generating state for all selected pages on error
                    setGenerateJobIds(prev => {
                        const newState = { ...prev };
                        selectedUrls.forEach(pageUrl => {
                            delete newState[pageUrl];
                        });
                        return newState;
                    });
                    failAlertRef.current?.show("Failed to start schema generation for selected pages. Please try again after some time.");
                    setTimeout(() => {
                        failAlertRef.current?.close();
                    }, 5000);
                },
            }
        );
    };

    const handleSaveSchema = (schema: string, url: string) => {
        saveSchema.mutate({
            schema: schema,
            url: url
        }, {
            onSuccess: () => {
                setShowSchemaModal(false)
                refetch()
                successAlertRef.current?.show("Schema Saved!");
                setTimeout(() => {
                    successAlertRef.current?.close();
                }, 5000);
            },
            onError: () => {
                setShowSchemaModal(false)
                failAlertRef.current?.show("Failed to save Schema. Please try again after some time.");
                setTimeout(() => {
                    failAlertRef.current?.close();
                }, 5000);
            }
        })
    }

    const handleToggleAutoSchema = (url: string, currentState: boolean) => {
        if (!pageData.tools_loading_script_verified) {
            failAlertRef.current?.show("Please add the script tag to your website first.");
            setTimeout(() => {
                failAlertRef.current?.close();
            }, 3000);
            return;
        }

        const newState = !currentState;

        // Optimistically update the toggle state
        setPageToggleStates(prevStates => {
            const newStates = new Map(prevStates);
            newStates.set(url, newState);
            return newStates;
        });

        updateSchemaSettingMutation.mutate(
            { url: url, schema_enabled: newState },
            {
                onSuccess: () => {
                    successAlertRef.current?.show(`Auto Schema ${newState ? 'enabled' : 'disabled'} for this page!`);
                    setTimeout(() => {
                        successAlertRef.current?.close();
                    }, 3000);
                    // Update the table data as well to keep it in sync
                    setTableData(prevData =>
                        prevData.map(page =>
                            page.url === url
                                ? { ...page, autoSchemaEnabled: newState }
                                : page
                        )
                    );
                },
                onError: (error) => {
                    console.error('Error updating schema setting:', error);
                    // Revert the optimistic update on error
                    setPageToggleStates(prevStates => {
                        const newStates = new Map(prevStates);
                        newStates.set(url, currentState);
                        return newStates;
                    });
                    failAlertRef.current?.show(`Failed to ${newState ? 'enable' : 'disable'} Auto Schema. Please try again.`);
                    setTimeout(() => {
                        failAlertRef.current?.close();
                    }, 5000);
                }
            }
        );
    }

    const handleBulkEnableDisableSchema = () => {
        if (selectedRows.size === 0) {
            failAlertRef.current?.show("Please select at least one page to enable/disable schema.");
            setTimeout(() => {
                failAlertRef.current?.close();
            }, 3000);
            return;
        }

        setShowBulkActionModal(true);
    }

    const handleBulkActionConfirm = () => {
        const selectedUrls = Array.from(selectedRows);
        const schemaEnabled = bulkAction === 'enable';

        // Store original states for potential rollback
        const originalStates = new Map<string, boolean>();
        selectedUrls.forEach(url => {
            const currentState = pageToggleStates.get(url) ?? (tableData.find(page => page.url === url)?.autoSchemaEnabled || false);
            originalStates.set(url, currentState);
        });

        // Optimistically update the toggle states
        setPageToggleStates(prevStates => {
            const newStates = new Map(prevStates);
            selectedUrls.forEach(url => {
                newStates.set(url, schemaEnabled);
            });
            return newStates;
        });

        // Clear selected rows immediately for better UX
        setSelectedRows(new Set());
        setSelectAll(false);
        setShowBulkActionModal(false);

        bulkUpdateSchemaSettingMutation.mutate(
            { urls: selectedUrls, schema_enabled: schemaEnabled },
            {
                onSuccess: (response) => {
                    const responseData = response.data;
                    if (responseData.success || responseData.message === "OK") {
                        successAlertRef.current?.show(`Auto Schema ${schemaEnabled ? 'enabled' : 'disabled'} for ${selectedUrls.length} selected page${selectedUrls.length > 1 ? 's' : ''}!`);
                        setTimeout(() => {
                            successAlertRef.current?.close();
                        }, 3000);
                        // Update the table data as well to keep it in sync
                        setTableData(prevData =>
                            prevData.map(page =>
                                selectedUrls.includes(page.url)
                                    ? { ...page, autoSchemaEnabled: schemaEnabled }
                                    : page
                            )
                        );
                    } else {
                        // Revert the optimistic update on error
                        setPageToggleStates(prevStates => {
                            const newStates = new Map(prevStates);
                            originalStates.forEach((originalState, url) => {
                                newStates.set(url, originalState);
                            });
                            return newStates;
                        });
                        failAlertRef.current?.show(responseData.message || "Failed to update schema settings. Please try again.");
                        setTimeout(() => {
                            failAlertRef.current?.close();
                        }, 5000);
                    }
                },
                onError: (error) => {
                    console.error('Error bulk updating schema settings:', error);
                    // Revert the optimistic update on error
                    setPageToggleStates(prevStates => {
                        const newStates = new Map(prevStates);
                        originalStates.forEach((originalState, url) => {
                            newStates.set(url, originalState);
                        });
                        return newStates;
                    });
                    const errorMessage = error?.response?.status === 500
                        ? "Server error occurred. Please try again later."
                        : error?.response?.data?.message || "Failed to update schema settings. Please try again.";

                    failAlertRef.current?.show(errorMessage);
                    setTimeout(() => {
                        failAlertRef.current?.close();
                    }, 5000);
                }
            }
        );
    }

    const handleToggleAutoScraping = (enabled: boolean) => {
        toggleAutoScanMutation.mutate(
            { auto_scan_enabled: enabled },
            {
                onSuccess: (response) => {
                    const responseData = response.data;
                    if (responseData.message === "OK") {
                        setAutoScrapingEnabled(responseData.auto_scan_enabled);
                        successAlertRef.current?.show(`Auto scraping ${responseData.auto_scan_enabled ? 'enabled' : 'disabled'}!`);
                        setTimeout(() => {
                            successAlertRef.current?.close();
                        }, 3000);
                    } else {
                        // Handle unexpected response
                        failAlertRef.current?.show("Failed to update auto scraping setting. Please try again.");
                        setTimeout(() => {
                            failAlertRef.current?.close();
                        }, 5000);
                    }
                },
                onError: (error) => {
                    console.error('Error toggling auto scraping:', error);
                    // Handle 500 and other non-success status codes
                    const errorMessage = error?.response?.status === 500
                        ? "Server error occurred. Please try again later."
                        : error?.response?.data?.message || "Failed to update auto scraping setting. Please try again.";

                    failAlertRef.current?.show(errorMessage);
                    setTimeout(() => {
                        failAlertRef.current?.close();
                    }, 5000);
                }
            }
        );
    }

    const handleToggleGlobalAutoSchema = (enabled: boolean) => {
        toggleAutoSchemaMut.mutate(
            { auto_schema_enabled: enabled },
            {
                onSuccess: (response) => {
                    const responseData = response.data;
                    if (responseData.message === "OK") {
                        setAutoSchemaEnabled(responseData.auto_schema_enabled);
                        successAlertRef.current?.show(`Auto schema ${responseData.auto_schema_enabled ? 'enabled' : 'disabled'} for all pages!`);
                        setTimeout(() => {
                            successAlertRef.current?.close();
                        }, 3000);
                    } else {
                        // Handle unexpected response
                        failAlertRef.current?.show("Failed to update auto schema setting. Please try again.");
                        setTimeout(() => {
                            failAlertRef.current?.close();
                        }, 5000);
                    }
                },
                onError: (error) => {
                    console.error('Error toggling auto schema:', error);
                    // Handle 500 and other non-success status codes
                    const errorMessage = error?.response?.status === 500
                        ? "Server error occurred. Please try again later."
                        : error?.response?.data?.message || "Failed to update auto schema setting. Please try again.";

                    failAlertRef.current?.show(errorMessage);
                    setTimeout(() => {
                        failAlertRef.current?.close();
                    }, 5000);
                }
            }
        );
    }

    const handleAddSitemapUrl = () => {
        let url: URL | null;
        try {
            url = new URL(sitemapUrl);
        } catch {
            url = null;
        }

        if (!url) {
            failAlertRef.current?.show("Please enter a valid URL.");
        } else if (!sitemapUrl.trim()) {
            failAlertRef.current?.show("Please enter the sitemap URL.");
        } else if (!isValidSitemapUrl(url)) {
            failAlertRef.current?.show("The provided URL is not a valid sitemap URL. It should end with .xml or .txt");
        } else if (url.hostname !== pageData.domain) {
            failAlertRef.current?.show("The sitemap URL must match the website's domain.");
        } else {
            addSiteMapMutation.mutate(sitemapUrl, {
                onSuccess: (response: any) => {
                    const responseData: any = response.data;

                    if (responseData.success) {
                        setShowSitemapUrlModal(false);
                        setIsCrawling(true);
                        successAlertRef.current?.show("Sitemap URL added successfully!");
                        setTimeout(() => {
                            successAlertRef.current?.close();
                        }, 5000);
                    } else {
                        failAlertRef.current?.show(responseData.message);
                        setTimeout(() => {
                            failAlertRef.current?.close();
                        }, 5000);
                    }

                },
                onError: (response) => {
                    console.log(response)
                    failAlertRef.current?.show("Failed to add sitemap url. Please try again after some time.");
                    setTimeout(() => {
                        failAlertRef.current?.close();
                    }, 5000);
                }
            });
        }
    };

    const handleAddMultipleSitemaps = () => {
        if (!newSitemaps.trim()) {
            failAlertRef.current?.show("Please enter at least one sitemap URL.");
            setTimeout(() => {
                failAlertRef.current?.close();
            }, 3000);
            return;
        }

        // Split by comma and space, then filter out empty strings
        const sitemapUrls = newSitemaps
            .split(/[,\s]+/)
            .map(url => url.trim())
            .filter(url => url.length > 0);

        if (sitemapUrls.length === 0) {
            failAlertRef.current?.show("Please enter valid sitemap URLs.");
            setTimeout(() => {
                failAlertRef.current?.close();
            }, 3000);
            return;
        }

        // Validate each URL
        const invalidUrls: string[] = [];
        const validUrls: string[] = [];

        sitemapUrls.forEach(urlString => {
            let url: URL | null;
            try {
                url = new URL(urlString);
            } catch {
                url = null;
            }

            if (!url) {
                invalidUrls.push(urlString);
            } else if (!isValidSitemapUrl(url)) {
                invalidUrls.push(urlString);
            } else if (url.hostname !== pageData.domain) {
                invalidUrls.push(urlString);
            } else {
                validUrls.push(urlString);
            }
        });

        if (invalidUrls.length > 0) {
            failAlertRef.current?.show(`Invalid URLs found: ${invalidUrls.join(', ')}. URLs must be valid sitemaps (.xml or .txt) from your domain.`);
            setTimeout(() => {
                failAlertRef.current?.close();
            }, 5000);
            return;
        }

        if (validUrls.length === 0) {
            failAlertRef.current?.show("No valid sitemap URLs found.");
            setTimeout(() => {
                failAlertRef.current?.close();
            }, 3000);
            return;
        }

        // Add all valid sitemap URLs in a single API call
        addSiteMapMutation.mutate(validUrls, {
            onSuccess: (response: any) => {
                const responseData: any = response.data;
                setShowAddSitemapsModal(false);
                setNewSitemaps("");

                if (responseData.success) {
                    setIsCrawling(true);
                    const count = validUrls.length;
                    successAlertRef.current?.show(`${count} sitemap${count > 1 ? 's' : ''} added successfully!`);
                    setTimeout(() => {
                        successAlertRef.current?.close();
                    }, 5000);
                } else {
                    failAlertRef.current?.show(responseData.message || "Failed to add sitemaps. Please try again.");
                    setTimeout(() => {
                        failAlertRef.current?.close();
                    }, 5000);
                }
            },
            onError: (error) => {
                console.error('Error adding sitemaps:', error);
                setShowAddSitemapsModal(false);
                setNewSitemaps("");

                failAlertRef.current?.show("Failed to add sitemaps. Please try again after some time.");
                setTimeout(() => {
                    failAlertRef.current?.close();
                }, 5000);
            }
        });
    };

    const handleStartScanningPages = () => {
        // Mark the tool as used and start scanning
        markToolAsUsedMut.mutate(
            { toolName: 'ai-auto-schema' },
            {
                onSuccess: () => {
                    setAutoSchemaUsed(true);
                },
                onError: (error) => {
                    console.error('Error marking tool as used:', error);
                    failAlertRef.current?.show("Failed to start scanning. Please try again.");
                    setTimeout(() => {
                        failAlertRef.current?.close();
                    }, 5000);
                }
            }
        );
    };

    // ---------------------- FUNCTIONS ----------------------
    function isValidSitemapUrl(parsedUrl: URL): boolean {
        try {
            return parsedUrl.pathname.endsWith('.xml') || parsedUrl.pathname.endsWith('.txt');
        } catch (error) {
            return false; // Return false if URL is not valid
        }
    }

    function getSnippetFileName() {
        if (process.env.REACT_APP_DRF_DOMAIN == 'https://api.abun.com') {
            return 'snippet.js';
        } else if (process.env.REACT_APP_DRF_DOMAIN == 'https://staging.api.abun.com') {
            return 'snippet-staging.js';
        } else {
            return 'snippet-dev.js';
        }
    }

    const getStepClass = (stepStatus: string | undefined) => {
        if (stepStatus === 'completed') return 'completed';
        if (stepStatus === 'in_progress') return 'active';
        return '';
    };

    const handleVerifyScript = () => {
        setIsVerifying(true);

        verifyScriptMutation.mutate(undefined, {
            onSuccess: (response: any) => {
                setIsVerifying(false);
                const responseData = response.data;

                if (responseData.success) {
                    setIsVerified(true);
                    successAlertRef.current?.show("Script verification successful! Your website is properly configured.");
                    setTimeout(() => {
                        successAlertRef.current?.close();
                    }, 5000);
                } else {
                    // Handle different error types based on err_id
                    let errorMessage = responseData.message || "Verification failed";

                    switch (responseData.err_id) {
                        case "NO_WEBSITE_FOUND":
                            errorMessage = "No website found. Please ensure your website is properly connected.";
                            break;
                        case "WEBSITE_NOT_ACCESSIBLE":
                            errorMessage = "Website not accessible. Please check if your website is online and accessible.";
                            break;
                        case "SCRIPT_TAG_NOT_FOUND":
                            errorMessage = "Script tag not found on your website. Please ensure the script is properly installed.";
                            break;
                        case "INVALID_USER_EMAIL":
                            errorMessage = "Invalid user email. Please contact support if this issue persists.";
                            break;
                        default:
                            errorMessage = responseData.message || "Verification failed. Please try again.";
                    }

                    failAlertRef.current?.show(errorMessage);
                    setTimeout(() => {
                        failAlertRef.current?.close();
                    }, 5000);
                }
            },
            onError: (error: any) => {
                setIsVerifying(false);
                console.error('Error verifying script:', error);

                const errorMessage = error?.response?.data?.message || "Failed to verify script. Please try again.";
                failAlertRef.current?.show(errorMessage);
                setTimeout(() => {
                    failAlertRef.current?.close();
                }, 5000);
            }
        });
    };

    const getJobStorageKey = (pageUrl: string, type: 'single' | 'regenerate') => {
        return `schema_job_${type}_${encodeURIComponent(pageUrl)}`;
    };

    const getBulkJobStorageKey = () => `schema_job_bulk_${pageData.domain}`;

    const saveJobToStorage = (pageUrl: string, jobId: string, type: 'single' | 'regenerate') => {
        if (pageUrl && jobId) {
            localStorage.setItem(getJobStorageKey(pageUrl, type), JSON.stringify({
                jobId,
                pageUrl,
                type,
                timestamp: Date.now()
            }));
        }
    };

    const saveBulkJobToStorage = (jobId: string, pagesUrls: string[]) => {
        if (jobId) {
            localStorage.setItem(getBulkJobStorageKey(), JSON.stringify({
                jobId,
                pagesUrls,
                type: 'bulk',
                timestamp: Date.now()
            }));
        }
    };

    const getBulkJobFromStorage = () => {
        const stored = localStorage.getItem(getBulkJobStorageKey());
        return stored ? JSON.parse(stored) : null;
    };

    const clearJobFromStorage = (pageUrl: string, type: 'single' | 'regenerate') => {
        localStorage.removeItem(getJobStorageKey(pageUrl, type));
    };

    const clearBulkJobFromStorage = () => {
        localStorage.removeItem(getBulkJobStorageKey());
    };

    const isJobTooOld = (timestamp: number) => {
        return Date.now() - timestamp > 24 * 60 * 60 * 1000; // 24 hours
    };

    // ============================================================
    // --------------------- MAIN RENDER CODE ---------------------
    // ============================================================

    // First-time user experience - show "Start Using Auto Schema" page
    if (!autoSchemaUsed) {
        return (
            <div className="ai-auto-schema-container">
                <div className="ai-auto-schema-box">
                    <h2>Start Using Auto Schema for {pageData.domain}</h2>
                    <p>Automatically generate and manage schema markup for better SEO</p>
                    <div className="ai-auto-schema-benefits">
                        <p className="benefits-title">Benefits of AI Auto Schema:</p>
                        <ul>
                            <li>Automatically generate schema markup for all your pages</li>
                            <li>Improve search engine understanding of your content</li>
                            <li>Enhance rich snippets in search results</li>
                            <li>Boost SEO performance with structured data</li>
                        </ul>
                    </div>
                    <div className="ai-auto-schema-privacy">
                        <span className="privacy-icon">🔒</span>
                        <div className="privacy-text">
                            <span className="privacy-title">Your data stays private</span>
                            <p>We only access your page content to generate schema. You control all generated markup.</p>
                        </div>
                    </div>
                    <div className="ai-auto-schema-action" style={{ textAlign: 'center', marginTop: '2rem' }}>
                        <AbunButton
                            type="primary"
                            className="ai-auto-schema-start-button"
                            style={{
                                padding: '12px 24px',
                                fontSize: '1.1rem',
                                fontWeight: '600',
                                borderRadius: '8px'
                            }}
                            disabled={markToolAsUsedMut.isLoading}
                            clickHandler={handleStartScanningPages}
                        >
                            {markToolAsUsedMut.isLoading ? "Starting..." : "Start Scanning Pages"}
                        </AbunButton>
                    </div>
                </div>
            </div>
        );
    }

    // Show scanning progress page if crawling is in progress
    if (pageData.is_crawling) {
        return (
            <div className="analysis-progress-container">
                <div className='w-100 is-flex is-justify-content-flex-start'>
                    <svg className={"back-btn"} onClick={handleBackClick} stroke="#bcbcbc" fill="#bcbcbc" width="28" height="24" viewBox="0 0 28 24">
                        <path d="M27.5 12H2M2 12L13 1M2 12L13 23" stroke="black" strokeOpacity="0.5" strokeWidth="2" />
                    </svg>
                </div>
                <div className="analysis-card">
                    <h1 className="analysis-title">Scanning Your Website Pages</h1>
                    <p className="analysis-subtitle">Our AI is currently scanning all the pages of your site to prepare them for schema generation.</p>

                    {
                        websiteAnalysisStats?.total_pages ?
                            <>
                                <h4 className={"is-size-5 has-text-centered"}>
                                    {analysisStepText}&nbsp;&nbsp;<Icon iconName="spinner" />
                                </h4>

                                <div className="progress-bar">
                                    <div className="progress-fill" style={{ width: `${websiteAnalysisStats.progress}%` }}></div>
                                </div>

                                <div className="analysis-stats">
                                    <div className="stat-item">
                                        <h2>{websiteAnalysisStats.progress}%</h2>
                                        <p>Progress</p>
                                    </div>
                                    <div className="stat-item">
                                        <h2>{websiteAnalysisStats.time_display}</h2>
                                        <p>Minutes Left</p>
                                    </div>
                                    <div className="stat-item">
                                        <h2>{websiteAnalysisStats.pages_scanned || 0}</h2>
                                        <p>Pages Scanned</p>
                                    </div>
                                </div>
                            </>
                            : (isScanMore && !scanMoreRunning) ? (<div style={{ textAlign: "center", marginTop: "1rem" }}>
                                <AbunButton
                                    className={"is-primary is-small"}
                                    disabled={scanMorePagesMutation.isLoading}
                                    type={"primary"} clickHandler={() => {
                                        scanMorePagesMutation.mutate(undefined, {
                                            onSuccess: () => {
                                                setIsCrawling(true);
                                                setIsScanMore(false)
                                                updateScanMoreRunning(true);
                                                setLoadingDataText("Finding Pages...")
                                            },
                                            onError: () => {
                                                failAlertRef.current?.show("Failed to scan more pages. Please try again after some time.");
                                                setTimeout(() => {
                                                    failAlertRef.current?.close();
                                                }, 5000);
                                            }
                                        });
                                    }}>
                                    Start Scan More!
                                </AbunButton>
                            </div>)
                                :
                                <>
                                    <h4 className={"is-size-5 has-text-centered"}>
                                        {loadingDataText}&nbsp;&nbsp;<Icon iconName="spinner" />
                                    </h4>
                                </>
                    }
                </div>

                <div className="analysis-steps-card">
                    <h3>Analysis Steps</h3>
                    <ul>
                        <li className={getStepClass(websiteAnalysisStats?.steps.crawling)}>
                            1. Scanning your website
                            <br />
                            <p>&nbsp;&nbsp;&nbsp; - We’re crawling all the pages on your site.</p>
                        </li>
                        <li className={getStepClass(websiteAnalysisStats?.steps.analyzing)}>
                            2. Understanding your content
                            <br />
                            <p>&nbsp;&nbsp;&nbsp; - Analyzing what each page is about.</p>
                        </li>
                    </ul>
                </div>

                <div className="next-steps-card">
                    <h3>Next up:</h3>
                    <p>Once the scan is complete, you’ll be able to generate schema markup for each page.</p>
                    <br />
                    <p>You can then view, edit, and copy the schema as needed.</p>
                    <br />
                    <b>Note:</b>
                    <br />
                    <p>The number of pages we scan depends on your current plan.</p>
                </div>
            </div>
        );
    }

    // Handle loading states and other conditions
    if (isFetching || isLoading) {
        // Show first-time user experience if finding sitemaps
        if (pageData.finding_sitemaps || loadingDataText === "Finding Sitemaps...") {
            return (
                <div className="ai-auto-schema-container">
                    <div className="ai-auto-schema-box">
                        <h2>AI Auto Schema for {pageData.domain}</h2>
                        <p>Connect your website to automatically generate and manage schema markup for better SEO</p>
                        <div className="ai-auto-schema-benefits">
                            <p className="benefits-title">Benefits of AI Auto Schema:</p>
                            <ul>
                                <li>Automatically generate schema markup for all your pages</li>
                                <li>Improve search engine understanding of your content</li>
                                <li>Enhance rich snippets in search results</li>
                                <li>Boost SEO performance with structured data</li>
                            </ul>
                        </div>
                        <div className="ai-auto-schema-privacy">
                            <span className="privacy-icon">🔒</span>
                            <div className="privacy-text">
                                <span className="privacy-title">Your data stays private</span>
                                <p>We only access your page content to generate schema. You control all generated markup.</p>
                            </div>
                        </div>
                        <div className="ai-auto-schema-status">
                            <span className="status-icon">🔍</span>
                            <div className="status-text">
                                <span className="status-title">Finding your website pages...</span>
                                <p>We're automatically discovering pages from your sitemap to generate schema markup.</p>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        return (
            <p style={{ textAlign: "center", fontSize: "1.3rem" }} className="mt-5">
                {loadingDataText}
            </p>
        );
    } else if (isError) {
        return (
            <section className="section">
                <div className="container">
                    <div className="box">
                        <h1 className="title has-text-centered">AI Auto Schema</h1>
                        <p className="has-text-centered is-size-5">
                            Failed to load data. Please try again later.
                        </p>
                    </div>
                </div>
            </section>
        );
    } else if (!basePageData.user_verified) {
        return (
            <section className="section">
                <div className="container">
                    <div className="box">
                        <h1 className="title has-text-centered">AI Auto Schema</h1>
                        <p className="has-text-centered is-size-5">
                            You will need to verify your email to use this feature.
                        </p>
                    </div>
                </div>
            </section>
        );
    }

    return (
        <>
            {/* Page Title */}
            <div className="mb-5 ai-article-header">
                <h2>AI Auto Schema</h2>
                <p>Automatically generate and manage schema markup for your website pages to improve SEO performance.<br />
                    Built to enhance search engine understanding of your content & boost rich snippets.<br />
                </p>
            </div>

            {/* Tabs Navigation */}
            <div className="tabs is-medium" style={{ scrollbarWidth: 'none' }}>
                <ul>
                    <li className={activeTab === "dashboard" ? "is-active" : ""}>
                        <a onClick={() => setActiveTab("dashboard")}>Dashboard</a>
                    </li>
                    <li className={activeTab === "schema-pages" ? "is-active" : ""}>
                        <a onClick={() => setActiveTab("schema-pages")}>Schema Pages</a>
                    </li>
                    <li className={activeTab === "settings-script" ? "is-active" : ""}>
                        <a onClick={() => setActiveTab("settings-script")}>Settings & Script Installation</a>
                    </li>
                </ul>
            </div>

            {/* Dashboard Tab */}
            {activeTab === "dashboard" && (
                <>
                    {/* Statistics Cards */}
                    {(pageData.total_pages_found !== undefined || pageData.total_pages_without_schema !== undefined || pageData.pages_with_auto_schema_live !== undefined) && (
                        <div className="stats-cards-container mt-1 mb-5">
                            <div className="stats-card">
                                <div className="stats-card-header">
                                    <Icon iconName="web-globe" width="16px" height="16px" />
                                    <h3 className="stats-card-title has-text-weight-medium">Total Pages Found</h3>
                                </div>
                                <hr />
                                <div className="stats-card-number">
                                    {pageData.total_pages_found || 0}
                                </div>
                                <div className="stats-card-subtitle">
                                    Your Total Website Pages. If you feel that there is any of the pages missing, go to settings and add sitemap link.
                                </div>
                            </div>
                            <div className="stats-card">
                                <div className="stats-card-header">
                                    <Icon iconName="delete" width="16px" height="16px" />
                                    <h3 className="stats-card-title has-text-weight-medium">Total Pages without Schema</h3>
                                </div>
                                <hr />
                                <div className="stats-card-number">
                                    {pageData.total_pages_without_schema || 0}
                                </div>
                                <div className="stats-card-subtitle">
                                    Total Pages where currently there was no schema found.
                                </div>
                            </div>
                            <div className="stats-card">
                                <div className="stats-card-header">
                                    <Icon iconName="green-checkmark-circle" width="16px" height="16px" />
                                    <h3 className="stats-card-title has-text-weight-medium">Pages with Auto Schema Live</h3>
                                </div>
                                <hr />
                                <div className="stats-card-number">
                                    {liveSchemaCount}
                                </div>
                                <div className="stats-card-subtitle">
                                    Total Pages where Schema is published & is live using Abun.
                                </div>
                            </div>
                            <div className="stats-card">
                                <div className="stats-card-header with-switch">
                                    <h3 className="stats-card-title has-text-weight-medium">Auto Find New URL</h3>
                                    <GlobalSwitch
                                        enabled={autoScrapingEnabled}
                                        handleToggle={handleToggleAutoScraping}
                                    />
                                </div>
                                <hr />
                                <div className="stats-card-number">
                                    <p className="stats-card-main-text">
                                        Next Run in 24 Hours
                                    </p>
                                    {autoScrapingEnabled && pageData.next_run_time && (
                                        <div className="stats-card-subtitle">
                                            Next run: {pageData.next_run_time}
                                        </div>
                                    )}
                                    <div className="stats-card-subtitle">
                                        We will fetch new pages on your site every 24 hours, create schema for those pages and publish the schema on those pages. All Automated.
                                    </div>
                                </div>
                            </div>
                            <div className="stats-card">
                                <div className="stats-card-header with-switch">
                                    <h3 className="stats-card-title has-text-weight-medium">Auto Schema</h3>
                                    <GlobalSwitch
                                        enabled={autoSchemaEnabled}
                                        handleToggle={handleToggleGlobalAutoSchema}
                                    />
                                </div>
                                <hr />
                                <div className="stats-card-number">
                                    <p className="stats-card-main-text">
                                        {autoSchemaEnabled ? 'Enabled' : 'Disabled'}
                                    </p>
                                    <div className="stats-card-subtitle">
                                        Enable or Disable Auto Schema across all of your entire pages.
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </>
            )}

            {/* Schema Pages Tab */}
            {activeTab === "schema-pages" && (
                <div className="tab-content w-100 website-scanning-container">
                    <AbunTable
                        ref={tableRef}
                        serverSide={true}
                        tableContentName={"Schema Data"}
                        apiUrl="/api/frontend/get-scraped-webpages/"
                        tableData={tableData}
                        columnDefs={columnDefs}
                        pageSizes={pageSizes}
                        enableSorting={true}
                        initialPageSize={pageSizes[1]}
                        transformResponse={(rawData) => ({
                            data: rawData.web_pages.map((page: any) => ({
                                url: page.url,
                                lastScanned: page.lastScanned,
                                schema: page.schema,
                                title: page.title,
                                schemaFound: page.schemaFound,
                                autoSchemaEnabled: page.autoSchemaEnabled,
                                schemaGenerated: page.schemaGenerated
                            })),
                            total: rawData.total_count
                        })}
                        noDataText={"No schema data available."}
                        searchboxPlaceholderText={"Search schema data..."}
                        buttons={(() => {
                            const selectedUrls = Array.from(selectedRows);
                            const hasSchemaGenerated = selectedUrls.some(url => {
                                const page = tableData.find(p => p.url === url);
                                return page?.schemaGenerated;
                            });
                            const hasNonSchemaGenerated = selectedUrls.some(url => {
                                const page = tableData.find(p => p.url === url);
                                return !page?.schemaGenerated;
                            });

                            return [
                                {
                                    text: selectedRows.size > 0 ? `Generate Schema (${selectedRows.size})` : "Generate Selected",
                                    type: "success",
                                    clickHandler: handleGenerateSelectedSchema,
                                    isDisabled: selectedRows.size === 0 || hasSchemaGenerated || isBulkGenerating || autoSchemaRemainingLimit === 0,
                                    extraClassName: "is-small is-justify-content-space-between",
                                },
                                {
                                    text: selectedRows.size > 0 ? `Bulk Enable/Disable (${selectedRows.size})` : "Bulk Enable/Disable",
                                    type: "success",
                                    clickHandler: handleBulkEnableDisableSchema,
                                    isDisabled: selectedRows.size === 0 || hasNonSchemaGenerated || bulkUpdateSchemaSettingMutation.isLoading,
                                    extraClassName: "is-small is-justify-content-space-between",
                                },
                                {
                                    text: isBulkGenerating ? "Generating..." : `Generate Schema for All Pages (${Math.min(pageData.total_pages_without_schema, autoSchemaRemainingLimit)} pages)`,
                                    type: "primary",
                                    clickHandler: handleBulkGenerateSchema,
                                    isDisabled: isBulkGenerating || generateSchemaMutation.isLoading || autoSchemaRemainingLimit === 0,
                                    extraClassName: "is-small is-justify-content-space-between",
                                }
                            ];
                        })()}
                    />
                </div>
            )}

            {/* Settings & Script Installation Tab */}
            {activeTab === "settings-script" && (
                <div className="tab-content w-100 website-scanning-container">
                    <div className="mt-4">
                        <h3 className="is-size-4 has-text-weight-semibold mb-3">Script Installation</h3>
                        <p className="mb-3">Generate and install the script tag on your website to enable auto schema functionality.</p>

                        {(!pageData.is_crawling) && basePageData.user_verified && (
                            <div className="script-installation-cards mt-4">
                                {/* Step 1: Add code snippets */}
                                <div className="script-card">
                                    <div className="script-card-header">
                                        <div className="script-step-number">1</div>
                                        <div className="script-step-content">
                                            <h4 className="script-step-title">Add code snippets</h4>
                                            <p className="script-step-description">Paste scripts into the &lt;head&gt; tag in all pages of your site</p>
                                        </div>
                                    </div>
                                    <div className="script-code-container">
                                        <pre className="script-code">
                                            <code>{`<script async src="${process.env.REACT_APP_FRONTEND_DOMAIN}/js/${getSnippetFileName()}" data-user-id="${pageData.encrypted_id}"></script>`}</code>
                                        </pre>
                                        <AbunButton
                                            type="primary"
                                            className="script-copy-button"
                                            clickHandler={() => {
                                                const scriptContent = `<script async src="${process.env.REACT_APP_FRONTEND_DOMAIN}/js/${getSnippetFileName()}" data-user-id="${pageData.encrypted_id}"></script>`;
                                                navigator.clipboard.writeText(scriptContent).then(() => {
                                                    successAlertRef.current?.show("Script copied to clipboard!");
                                                    setTimeout(() => {
                                                        successAlertRef.current?.close();
                                                    }, 3000);
                                                }).catch(() => {
                                                    failAlertRef.current?.show("Failed to copy script. Please try again.");
                                                    setTimeout(() => {
                                                        failAlertRef.current?.close();
                                                    }, 3000);
                                                });
                                            }}>
                                            <Icon iconName="copy" width="14px" height="14px" style={{ marginRight: '6px' }} />
                                            Copy
                                        </AbunButton>
                                    </div>
                                </div>

                                {/* Step 2: Verify setup */}
                                <div className="script-card">
                                    <div className="script-card-header">
                                        <div className="script-step-number">2</div>
                                        <div className="script-step-content">
                                            <h4 className="script-step-title">Verify setup</h4>
                                            <p className="script-step-description">Paste your site's URL to verify script installation</p>
                                        </div>
                                    </div>
                                    <div className="script-verify-container">
                                        <AbunButton
                                            className="script-verify-button"
                                            style={{ background: '#10B981', borderColor: '#10B981' }}
                                            type="primary"
                                            clickHandler={handleVerifyScript}
                                            disabled={isVerifying || isVerified}>
                                            {isVerified ? 'Verified' : isVerifying ? 'Verifying...' : 'Verify'}
                                        </AbunButton>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    <div className="mt-5">
                        <h3 className="is-size-4 has-text-weight-semibold mb-3">Sitemap Management</h3>
                        <p className="mb-3">Add additional sitemaps to scan more pages from your website.</p>

                        <div className="is-flex is-align-items-center mb-4">
                            <AbunButton
                                className={"is-primary is-outlined is-small"}
                                type={"primary"}
                                clickHandler={() => setShowAddSitemapsModal(true)}
                            >
                                <span style={{ fontSize: '1.25rem', marginRight: '4px', marginBottom: '3px' }}>+</span> Add More Sitemaps
                            </AbunButton>
                        </div>

                        {pageData.sitemap_urls && pageData.sitemap_urls.length > 0 && (
                            <div className="box">
                                <h4 className="is-size-5 has-text-weight-semibold mb-3">Current Sitemaps:</h4>
                                <ul>
                                    {pageData.sitemap_urls.map((sitemap, index) => (
                                        <li key={index} className="mb-2">
                                            <a href={sitemap} target="_blank" rel="noopener noreferrer" className="has-text-link">
                                                {sitemap}
                                            </a>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        )}
                    </div>

                    {/* Scan More Section */}
                    {(!pageData.is_crawling || scanMoreRunning) && pageData.has_more_pages && basePageData.currentPlanName !== 'Trial' && (
                        <div className="mt-5">
                            <h3 className="is-size-4 has-text-weight-semibold mb-3">Scan More Pages</h3>
                            <p className="mb-3">Discover additional pages from your website to generate schema markup for more content.</p>

                            <div className="is-flex is-align-items-center mb-4">
                                <AbunButton
                                    className={"is-primary is-small"}
                                    style={{ background: '#2E64FE' }}
                                    disabled={scanMorePagesMutation.isLoading}
                                    type={"primary"}
                                    clickHandler={() => {
                                        scanMorePagesMutation.mutate(undefined, {
                                            onSuccess: () => {
                                                setIsCrawling(true);
                                                setIsScanMore(false)
                                                updateScanMoreRunning(true);
                                                setLoadingDataText("Finding Pages...")
                                            },
                                            onError: () => {
                                                failAlertRef.current?.show("Failed to scan more pages. Please try again after some time.");
                                                setTimeout(() => {
                                                    failAlertRef.current?.close();
                                                }, 5000);
                                            }
                                        })
                                    }}
                                >
                                    Scan More Pages
                                </AbunButton>
                            </div>
                        </div>
                    )}
                </div>
            )}

            {/* Modals */}

            {/* Add Sitemap Url Modal */}
            <AbunModal
                active={showSitemapUrlModal}
                headerText={"Add Your Website Sitemap"}
                closeable={true}
                hideModal={() => {
                    navigate(pageURL["createArticle"]);
                }}
            >
                <div className="has-text-centered">
                    <div className="field">
                        <div className="control">
                            <input
                                type="url"
                                className="input"
                                placeholder="Enter your website sitemap url here..."
                                value={sitemapUrl}
                                onChange={(e) => setSitemapUrl(e.target.value)}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                        handleAddSitemapUrl();
                                    }
                                }}
                            />
                        </div>
                    </div>

                    <AbunButton
                        type="success"
                        className="mt-4"
                        disabled={!sitemapUrl.trim() || addSiteMapMutation.isLoading || !basePageData.user_verified} // Disable if input is empty
                        clickHandler={handleAddSitemapUrl}
                    >
                        {addSiteMapMutation.isLoading ? "Adding..." : "Add"}
                    </AbunButton>
                </div>
            </AbunModal>

            <AbunModal active={showSchemaModal}
                headerText={`Schema Markup For: ${editedSchemaPageTitle}`}
                closeable={true}
                closeableKey={true}
                modelWidth="800px"
                hideModal={() => {
                    setShowSchemaModal(false)
                }}>
                <div className={"has-text-centered"}>
                    <textarea
                        className="abun-script mt-2"
                        value={editedSchema}
                        onChange={(e) => {
                            setEditedSchema(e.target.value);
                            adjustTextareaHeight();
                        }}
                        ref={textareaRef}
                        style={{
                            width: "100%",
                            resize: "none",
                            border: "1px solid #ccc",
                            padding: "10px",
                            fontFamily: "$secondary-font",
                            overflow: "hidden", // Hide scrollbar
                            minHeight: "3rem", // Initial height
                        }}
                    />
                    <div className="is-flex is-justify-content-flex-start mt-4">
                        <AbunButton type={"success"}
                            className={"mr-4"}
                            disabled={saveSchema.isLoading}
                            clickHandler={() => { handleSaveSchema(editedSchema, regenerateLink) }}
                        >
                            {saveSchema.isLoading ? "Saving..." : "Save"}
                        </AbunButton>
                        <AbunButton type={"primary"}
                            className={"mr-2"}
                            disabled={disabledButtons[regenerateLink]}
                            clickHandler={() => handleRegenerate(regenerateLink)}
                        >
                            {disabledButtons[regenerateLink] ? "Regenerating..." : "Scan Page Again & Regenerate"}
                        </AbunButton>
                        {/* <AbunButton type={"primary"}
                            clickHandler={() => {
                                navigator.clipboard.writeText(editedSchema);
                                successAlertRef.current?.show("Schema copied to clipboard!");
                                setTimeout(() => {
                                    successAlertRef.current?.close();
                                }, 3000);
                            }}
                        >
                            Copy Schema
                        </AbunButton> */}
                    </div>
                </div>
            </AbunModal>

            {/* Add Multiple Sitemaps Modal */}
            <AbunModal
                active={showAddSitemapsModal}
                headerText={"Add More Sitemaps"}
                closeable={true}
                closeableKey={true}
                hideModal={() => {
                    setShowAddSitemapsModal(false);
                    setNewSitemaps("");
                }}
            >
                <div className="has-text-centered">
                    <div className="field">
                        <div className="control">
                            <textarea
                                className="textarea"
                                style={{ borderRadius: '10px', minHeight: '120px' }}
                                placeholder="Enter multiple sitemap URLs separated by commas or spaces..."
                                value={newSitemaps}
                                onChange={(e) => setNewSitemaps(e.target.value)}
                            />
                        </div>
                    </div>

                    <AbunButton
                        type="success"
                        className="mt-4"
                        disabled={!newSitemaps.trim() || addSiteMapMutation.isLoading}
                        clickHandler={handleAddMultipleSitemaps}
                    >
                        {addSiteMapMutation.isLoading ? "Adding..." : "Add Sitemaps"}
                    </AbunButton>
                </div>
            </AbunModal>

            {/* Bulk Action Modal */}
            <AbunModal
                active={showBulkActionModal}
                headerText={"Bulk Enable/Disable Schema"}
                closeable={true}
                closeableKey={true}
                hideModal={() => {
                    setShowBulkActionModal(false);
                }}
            >
                <div className="has-text-centered">
                    <p className="mb-4">
                        Choose the action you want to perform on {selectedRows.size} selected page{selectedRows.size > 1 ? 's' : ''}:
                    </p>

                    <div className="field">
                        <div className="control">
                            <div className="select is-fullwidth">
                                <select
                                    value={bulkAction}
                                    onChange={(e) => setBulkAction(e.target.value as 'enable' | 'disable')}
                                >
                                    <option value="enable">Enable All Selected URLs</option>
                                    <option value="disable">Disable All Selected URLs</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div className="buttons is-centered mt-4">
                        <AbunButton
                            type="light"
                            clickHandler={() => setShowBulkActionModal(false)}
                        >
                            Cancel
                        </AbunButton>
                        <AbunButton
                            type="primary"
                            disabled={bulkUpdateSchemaSettingMutation.isLoading}
                            clickHandler={handleBulkActionConfirm}
                        >
                            {bulkUpdateSchemaSettingMutation.isLoading ? "Processing..." : "Confirm"}
                        </AbunButton>
                    </div>
                </div>
            </AbunModal>

            <SuccessAlert ref={successAlertRef} />
            <ErrorAlert ref={failAlertRef} />

            {showConnectWebsiteModal && (
                <ConnectWebsite
                    setShowConnectWebsiteModal={setShowConnectWebsiteModal}
                    failAlertRef={failAlertRef}
                    successAlertRef={successAlertRef}
                    navigateOrReload="reload"
                />
            )}
        </>
    );
}

export default withAdminAndProductionCheck(AIAutoSchema);
